@echo off
setlocal enabledelayedexpansion

title Cloudflare IPv4 IP List Updater

echo ======================================================
echo           Cloudflare IPv4 IP List Updater
echo ======================================================
echo.

rem Check internet connection first
echo Checking internet connection...
ping -n 1 ******* >nul 2>&1
if errorlevel 1 (
    echo X No internet connection detected. Please check your network.
    echo.
    pause
    exit /b 1
)
echo + Internet connection confirmed
echo.

rem Create ipv4 folder if it doesn't exist
if not exist ipv4 mkdir ipv4

rem Backup existing files if they exist
if exist ipv4\*.txt (
    echo Creating backup of existing files...
    if not exist ipv4\backup mkdir ipv4\backup
    copy ipv4\*.txt ipv4\backup\ >nul 2>&1
    echo + Backup created in ipv4\backup\
    echo.
)

echo Updating IPv4 IP lists from GitHub Gist...
echo Source: https://gist.github.com/xczjzhu/8f10162261172c9937506940d79fb0f8
echo.

rem Base URL for the GitHub Gist
set base_url=https://gist.githubusercontent.com/xczjzhu/8f10162261172c9937506940d79fb0f8/raw

rem Initialize counters
set success_count=0
set fail_count=0

echo Downloading AS_HK.txt...
powershell -Command "Invoke-WebRequest -Uri '%base_url%/AS_HK.txt' -OutFile 'ipv4\AS_HK.txt' -UseBasicParsing"
if exist ipv4\AS_HK.txt (
    echo + AS_HK.txt downloaded successfully
    set /a success_count+=1
) else (
    echo X Failed to download AS_HK.txt
    set /a fail_count+=1
)

echo Downloading AS_JP.txt...
powershell -Command "Invoke-WebRequest -Uri '%base_url%/AS_JP.txt' -OutFile 'ipv4\AS_JP.txt' -UseBasicParsing"
if exist ipv4\AS_JP.txt (
    echo + AS_JP.txt downloaded successfully
    set /a success_count+=1
) else (
    echo X Failed to download AS_JP.txt
    set /a fail_count+=1
)

echo Downloading AS_KR.txt...
powershell -Command "Invoke-WebRequest -Uri '%base_url%/AS_KR.txt' -OutFile 'ipv4\AS_KR.txt' -UseBasicParsing"
if exist ipv4\AS_KR.txt (
    echo + AS_KR.txt downloaded successfully
    set /a success_count+=1
) else (
    echo X Failed to download AS_KR.txt
    set /a fail_count+=1
)

echo Downloading AS_SG.txt...
powershell -Command "Invoke-WebRequest -Uri '%base_url%/AS_SG.txt' -OutFile 'ipv4\AS_SG.txt' -UseBasicParsing"
if exist ipv4\AS_SG.txt (
    echo + AS_SG.txt downloaded successfully
    set /a success_count+=1
) else (
    echo X Failed to download AS_SG.txt
    set /a fail_count+=1
)

echo Downloading US_USA.txt...
powershell -Command "Invoke-WebRequest -Uri '%base_url%/US_USA.txt' -OutFile 'ipv4\US_USA.txt' -UseBasicParsing"
if exist ipv4\US_USA.txt (
    echo + US_USA.txt downloaded successfully
    set /a success_count+=1
) else (
    echo X Failed to download US_USA.txt
    set /a fail_count+=1
)

echo Downloading EU_WEST.txt...
powershell -Command "Invoke-WebRequest -Uri '%base_url%/EU_WEST.txt' -OutFile 'ipv4\EU_WEST.txt' -UseBasicParsing"
if exist ipv4\EU_WEST.txt (
    echo + EU_WEST.txt downloaded successfully
    set /a success_count+=1
) else (
    echo X Failed to download EU_WEST.txt
    set /a fail_count+=1
)

echo Downloading EU_CENTRAL.txt...
powershell -Command "Invoke-WebRequest -Uri '%base_url%/EU_CENTRAL.txt' -OutFile 'ipv4\EU_CENTRAL.txt' -UseBasicParsing"
if exist ipv4\EU_CENTRAL.txt (
    echo + EU_CENTRAL.txt downloaded successfully
    set /a success_count+=1
) else (
    echo X Failed to download EU_CENTRAL.txt
    set /a fail_count+=1
)

echo Downloading EU_EAST.txt...
powershell -Command "Invoke-WebRequest -Uri '%base_url%/EU_EAST.txt' -OutFile 'ipv4\EU_EAST.txt' -UseBasicParsing"
if exist ipv4\EU_EAST.txt (
    echo + EU_EAST.txt downloaded successfully
    set /a success_count+=1
) else (
    echo X Failed to download EU_EAST.txt
    set /a fail_count+=1
)

echo Downloading OTHER.txt...
powershell -Command "Invoke-WebRequest -Uri '%base_url%/OTHER.txt' -OutFile 'ipv4\OTHER.txt' -UseBasicParsing"
if exist ipv4\OTHER.txt (
    echo + OTHER.txt downloaded successfully
    set /a success_count+=1
) else (
    echo X Failed to download OTHER.txt
    set /a fail_count+=1
)
echo.

echo ======================================================
echo                Update Completed
echo ======================================================
echo.

rem Show download statistics
echo Download Statistics:
echo - Successful: !success_count!/9
echo - Failed: !fail_count!/9
echo.

rem Show summary of downloaded files
echo Summary of IPv4 IP list files:
set total_ips=0

if exist ipv4\AS_HK.txt (
    for /f %%i in ('find /c /v "" ^< ipv4\AS_HK.txt 2^>nul') do set file_lines=%%i
    echo + AS_HK.txt: !file_lines! IPs
    set /a total_ips+=!file_lines!
) else (
    echo X AS_HK.txt: Not available
)

if exist ipv4\AS_JP.txt (
    for /f %%i in ('find /c /v "" ^< ipv4\AS_JP.txt 2^>nul') do set file_lines=%%i
    echo + AS_JP.txt: !file_lines! IPs
    set /a total_ips+=!file_lines!
) else (
    echo X AS_JP.txt: Not available
)

if exist ipv4\AS_KR.txt (
    for /f %%i in ('find /c /v "" ^< ipv4\AS_KR.txt 2^>nul') do set file_lines=%%i
    echo + AS_KR.txt: !file_lines! IPs
    set /a total_ips+=!file_lines!
) else (
    echo X AS_KR.txt: Not available
)

if exist ipv4\AS_SG.txt (
    for /f %%i in ('find /c /v "" ^< ipv4\AS_SG.txt 2^>nul') do set file_lines=%%i
    echo + AS_SG.txt: !file_lines! IPs
    set /a total_ips+=!file_lines!
) else (
    echo X AS_SG.txt: Not available
)

if exist ipv4\US_USA.txt (
    for /f %%i in ('find /c /v "" ^< ipv4\US_USA.txt 2^>nul') do set file_lines=%%i
    echo + US_USA.txt: !file_lines! IPs
    set /a total_ips+=!file_lines!
) else (
    echo X US_USA.txt: Not available
)

if exist ipv4\EU_WEST.txt (
    for /f %%i in ('find /c /v "" ^< ipv4\EU_WEST.txt 2^>nul') do set file_lines=%%i
    echo + EU_WEST.txt: !file_lines! IPs
    set /a total_ips+=!file_lines!
) else (
    echo X EU_WEST.txt: Not available
)

if exist ipv4\EU_CENTRAL.txt (
    for /f %%i in ('find /c /v "" ^< ipv4\EU_CENTRAL.txt 2^>nul') do set file_lines=%%i
    echo + EU_CENTRAL.txt: !file_lines! IPs
    set /a total_ips+=!file_lines!
) else (
    echo X EU_CENTRAL.txt: Not available
)

if exist ipv4\EU_EAST.txt (
    for /f %%i in ('find /c /v "" ^< ipv4\EU_EAST.txt 2^>nul') do set file_lines=%%i
    echo + EU_EAST.txt: !file_lines! IPs
    set /a total_ips+=!file_lines!
) else (
    echo X EU_EAST.txt: Not available
)

if exist ipv4\OTHER.txt (
    for /f %%i in ('find /c /v "" ^< ipv4\OTHER.txt 2^>nul') do set file_lines=%%i
    echo + OTHER.txt: !file_lines! IPs
    set /a total_ips+=!file_lines!
) else (
    echo X OTHER.txt: Not available
)

echo.
echo Total IPs downloaded: !total_ips!
echo Files saved in: ipv4\ folder

rem Show completion status
if !fail_count! equ 0 (
    echo.
    echo + All IPv4 IP lists have been successfully updated!
    echo   You can now run cfiptest.bat or cfiptest_auto.bat
) else (
    echo.
    echo ! Some downloads failed. You may want to:
    echo   1. Check your internet connection
    echo   2. Try running this script again
    echo   3. Manually verify the GitHub Gist is accessible
    if exist ipv4\backup (
        echo   4. Restore from backup if needed: ipv4\backup\
    )
)

echo.
echo Current time: %date% %time%
echo.
echo Press any key to exit...
pause >nul
