#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Upload cfips_common.txt to GitHub Gist
"""

import os
import requests
import json

# Configuration
GITHUB_TOKEN = "****************************************"
GIST_ID = "8f10162261172c9937506940d79fb0f8"
FILE_PATH = "cfips_common.txt"

def upload_to_gist(file_path):
    """上传文件到GitHub Gist"""
    if not GITHUB_TOKEN:
        print("未配置GITHUB_TOKEN，跳过Gist上传")
        return False

    print(f"开始上传 {file_path} 到GitHub Gist...")

    try:
        # Check if file exists
        if not os.path.exists(file_path):
            print(f"文件 {file_path} 不存在")
            return False

        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        headers = {
            "Authorization": f"token {GITHUB_TOKEN}",
            "Accept": "application/vnd.github+json",
            "Content-Type": "application/json"
        }

        data = {
            "files": {
                os.path.basename(file_path): {
                    "content": content
                }
            }
        }

        print(f"发送PATCH请求到: https://api.github.com/gists/{GIST_ID}")
        print(f"文件名: {os.path.basename(file_path)}")
        print(f"内容长度: {len(content)} 字符")

        response = requests.patch(
            f"https://api.github.com/gists/{GIST_ID}",
            headers=headers,
            json=data
        )

        print(f"响应状态码: {response.status_code}")

        if response.status_code in (200, 201):
            print(f"{file_path} 上传到GitHub Gist成功")
            print(f"Gist URL: https://gist.github.com/xczjzhu/{GIST_ID}")
            print(f"Raw URL: https://gist.githubusercontent.com/xczjzhu/{GIST_ID}/raw/cfips_common.txt")
            return True
        else:
            print(f"{file_path} 上传到GitHub Gist失败")
            print(f"状态码: {response.status_code}")
            try:
                error_info = response.json()
                print(f"错误信息: {json.dumps(error_info, indent=2)}")
            except:
                print(f"响应内容: {response.text}")
            return False

    except Exception as e:
        print(f"{file_path} 上传到GitHub Gist出错: {str(e)}")
        return False

if __name__ == "__main__":
    # Check if cfips_common.txt exists, if not try to convert from CSV
    if not os.path.exists(FILE_PATH):
        print(f"{FILE_PATH} 不存在，尝试从 cfips_common.csv 转换...")
        if os.path.exists("cfips_common.csv"):
            print("转换 CSV 到上传格式...")
            with open("cfips_common.csv", 'r', encoding='utf-8') as csv_file:
                lines = csv_file.readlines()
            
            with open(FILE_PATH, 'w', encoding='utf-8') as txt_file:
                for line in lines[1:]:  # Skip header
                    line = line.strip()
                    if line:
                        parts = line.split(',')
                        if len(parts) >= 6:
                            ip = parts[0].strip()
                            port = parts[1].strip()
                            city = parts[5].strip()
                            txt_file.write(f"{ip}:{port}#{city}\n")
            print(f"转换完成，生成 {FILE_PATH}")
        else:
            print("cfips_common.csv 也不存在，请先运行测试脚本生成文件")
            exit(1)
    
    # Upload to gist
    success = upload_to_gist(FILE_PATH)
    if success:
        print("\n上传成功！")
    else:
        print("\n上传失败，请检查配置和网络连接")
