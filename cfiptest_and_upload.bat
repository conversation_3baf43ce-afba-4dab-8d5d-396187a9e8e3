@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

title Test Common Regions and Upload to Gist

echo ======================================================
echo     Test Common Regions and Upload to GitHub Gist
echo ======================================================
echo.
echo This script will:
echo 1. Test Hong Kong, Japan, Singapore and IPv6
echo 2. Generate cfips_common.csv in root directory
echo 3. Convert to upload format for GitHub Gist
echo.

set /p choice="Do you want to continue? (Y/N): "
if /i not "%choice%"=="Y" (
    echo Operation cancelled.
    pause
    exit /b 0
)

echo.
echo ======================================================
echo              Step 1: Testing Common Regions
echo ======================================================
echo.

rem Run the common regions test
call cfiptest_common_regions.bat

if not exist "cfips_common.csv" (
    echo.
    echo Error: cfips_common.csv was not generated!
    echo Please check the test results and try again.
    pause
    exit /b 1
)

echo.
echo ======================================================
echo              Step 2: Preparing Upload Format
echo ======================================================
echo.

rem Run the upload preparation script
call upload_to_gist_auto.bat

echo.
echo ======================================================
echo                   Workflow Completed
echo ======================================================
echo.
echo Files generated:
echo - cfips_common.csv (CSV format in root directory)
echo - cfips_common.txt (Upload format: IP:Port#Region)
echo.
echo Next steps:
echo 1. Go to: https://gist.github.com/xczjzhu/8f10162261172c9937506940d79fb0f8
echo 2. Click "Edit" button
echo 3. Find or create file "cfips_common.txt"
echo 4. Copy content from cfips_common.txt and paste it
echo 5. Click "Update public gist" to save
echo.
echo After upload, access the file at:
echo https://gist.githubusercontent.com/xczjzhu/8f10162261172c9937506940d79fb0f8/raw/cfips_common.txt
echo.
echo Press any key to exit...
pause >nul
