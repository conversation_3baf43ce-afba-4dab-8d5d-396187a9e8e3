#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cloudflare IP Test Tool - Python Implementation
支持IPv4和IPv6测试，自动上传到GitHub Gist
"""

import os
import sys
import argparse
import subprocess
import json
import time
import csv
from pathlib import Path
from urllib.request import urlopen, Request, HTTPError
from urllib.parse import urlencode

# 配置
GITHUB_TOKEN = "****************************************"
GIST_ID = "8f10162261172c9937506940d79fb0f8"
GITHUB_USER = "xczjzhu"

# 地区配置
REGIONS = {
    'AS_HK': '香港',
    'AS_JP': '日本', 
    'AS_KR': '韩国',
    'AS_SG': '新加坡',
    'US_USA': '美国',
    'EU_WEST': '欧洲西部',
    'EU_CENTRAL': '欧洲中部', 
    'EU_EAST': '欧洲东部',
    'OTHER': '其他地区'
}

COMMON_REGIONS = ['AS_HK', 'AS_JP', 'AS_SG']

class CFIPTest:
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent
        self.ipv4_dir = self.base_dir / 'ipv4'
        self.ipv6_dir = self.base_dir / 'ipv6'
        self.result_dir = self.base_dir / 'result'
        self.cfiptest_exe = self.base_dir / 'cfiptest.exe'
        self.cf_exe = self.ipv6_dir / 'cf.exe'
        
        # 确保目录存在
        self.result_dir.mkdir(exist_ok=True)
        
    def log(self, message, level="INFO"):
        """日志输出"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
        
    def check_internet(self):
        """检查网络连接"""
        try:
            result = subprocess.run(['ping', '-n', '1', '*******'], 
                                  capture_output=True, timeout=10)
            return result.returncode == 0
        except:
            return False
            
    def check_ipv6(self):
        """检查IPv6连接"""
        try:
            result = subprocess.run(['ping', '-6', '-n', '1', '2606:4700:4700::1111'], 
                                  capture_output=True, timeout=10)
            return result.returncode == 0
        except:
            return False
            
    def download_ip_lists(self):
        """下载最新的IP列表"""
        self.log("开始下载最新IP列表...")
        
        if not self.check_internet():
            self.log("网络连接失败", "ERROR")
            return False
            
        self.ipv4_dir.mkdir(exist_ok=True)
        base_url = "https://gist.githubusercontent.com/xczjzhu/8f10162261172c9937506940d79fb0f8/raw"
        
        success_count = 0
        total_ips = 0
        
        for region in REGIONS:
            try:
                url = f"{base_url}/{region}.txt"
                self.log(f"下载 {region}.txt...")
                
                req = Request(url, headers={'User-Agent': 'CFIPTest-Python'})
                with urlopen(req, timeout=30) as response:
                    content = response.read().decode('utf-8')
                    
                file_path = self.ipv4_dir / f"{region}.txt"
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                    
                lines = len([line for line in content.split('\n') if line.strip()])
                self.log(f"✓ {region}.txt: {lines} IPs")
                success_count += 1
                total_ips += lines
                
            except Exception as e:
                self.log(f"✗ 下载 {region}.txt 失败: {e}", "ERROR")
                
        self.log(f"下载完成: {success_count}/{len(REGIONS)} 成功, 总计 {total_ips} IPs")
        return success_count > 0
        
    def test_region(self, region, test_count=3, max_delay=200, min_speed=10):
        """测试指定地区"""
        ipv4_file = self.ipv4_dir / f"{region}.txt"
        if not ipv4_file.exists():
            self.log(f"IP文件不存在: {ipv4_file}", "ERROR")
            return None
            
        result_file = self.result_dir / f"ipv4_{region}_result.csv"
        
        cmd = [
            str(self.cfiptest_exe),
            '-f', str(ipv4_file),
            '-url', 'https://qiqitest.8699.pp.ua/200m',
            '-delay_url', 'qiqitest.8699.pp.ua',
            '-maxsc', str(test_count),
            '-maxdc', str(max_delay),
            '-mins', str(min_speed),
            '-o', str(result_file)
        ]
        
        self.log(f"开始测试 {REGIONS.get(region, region)}...")
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            if result.returncode == 0 and result_file.exists():
                self.log(f"✓ {REGIONS.get(region, region)} 测试完成")
                return result_file
            else:
                self.log(f"✗ {REGIONS.get(region, region)} 测试失败", "ERROR")
                return None
        except subprocess.TimeoutExpired:
            self.log(f"✗ {REGIONS.get(region, region)} 测试超时", "ERROR")
            return None
        except Exception as e:
            self.log(f"✗ {REGIONS.get(region, region)} 测试出错: {e}", "ERROR")
            return None

    def test_ipv6(self, test_count=3, max_delay=200, min_speed=10):
        """测试IPv6"""
        if not self.check_ipv6():
            self.log("IPv6连接不可用，跳过IPv6测试")
            return None

        self.ipv6_dir.mkdir(exist_ok=True)
        ipv6_cidr_file = self.ipv6_dir / 'ips-v6.txt'
        ipv6_generated_file = self.ipv6_dir / 'generated_ipv6.txt'

        if not ipv6_cidr_file.exists():
            self.log("IPv6 CIDR文件不存在，跳过IPv6测试")
            return None

        self.log("生成IPv6地址...")
        try:
            # 切换到ipv6目录
            original_dir = os.getcwd()
            os.chdir(self.ipv6_dir)

            # 生成IPv6地址
            cmd = [str(self.cf_exe), '-ips', '6', '-outfile', 'generated_ipv6.txt',
                   '-random', 'true', '-task', '100']
            result = subprocess.run(cmd, capture_output=True, timeout=60)

            if result.returncode == 0 and ipv6_generated_file.exists():
                # 处理生成的文件，只保留IP地址
                with open(ipv6_generated_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                clean_ips = []
                for line in lines:
                    if ',' in line and not line.startswith('IP'):
                        ip = line.split(',')[0].strip()
                        if ip and ':' in ip:
                            clean_ips.append(ip)

                with open(ipv6_generated_file, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(clean_ips))

                self.log(f"生成了 {len(clean_ips)} 个IPv6地址")
            else:
                self.log("IPv6地址生成失败", "ERROR")
                return None

        finally:
            os.chdir(original_dir)

        # 测试IPv6
        result_file = self.result_dir / 'ipv6_result.csv'
        cmd = [
            str(self.cfiptest_exe),
            '-f', str(ipv6_generated_file),
            '-url', 'https://qiqitest.8699.pp.ua/200m',
            '-delay_url', 'qiqitest.8699.pp.ua',
            '-maxsc', str(test_count),
            '-maxdc', str(max_delay),
            '-mins', str(min_speed),
            '-o', str(result_file)
        ]

        self.log("开始测试IPv6...")
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            if result.returncode == 0 and result_file.exists():
                self.log("✓ IPv6测试完成")
                return result_file
            else:
                self.log("✗ IPv6测试失败", "ERROR")
                return None
        except Exception as e:
            self.log(f"✗ IPv6测试出错: {e}", "ERROR")
            return None

    def merge_results(self, result_files, output_file):
        """合并测试结果"""
        self.log(f"合并结果到 {output_file}...")

        all_results = []
        header_written = False

        for result_file in result_files:
            if result_file and result_file.exists():
                try:
                    with open(result_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()

                    if lines:
                        if not header_written:
                            all_results.append(lines[0])  # 添加表头
                            header_written = True
                        all_results.extend(lines[1:])  # 添加数据行

                except Exception as e:
                    self.log(f"读取结果文件失败 {result_file}: {e}", "ERROR")

        if all_results:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.writelines(all_results)
            self.log(f"✓ 结果已合并到 {output_file}")
            return True
        else:
            self.log("没有有效的结果文件可合并", "ERROR")
            return False

    def convert_to_upload_format(self, csv_file, output_file):
        """转换CSV为上传格式 (IP:Port#Region)"""
        self.log(f"转换 {csv_file} 为上传格式...")

        try:
            upload_lines = []
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                next(reader)  # 跳过表头

                for row in reader:
                    if len(row) >= 6 and row[0].strip() and row[1].strip():
                        ip = row[0].strip()
                        port = row[1].strip()
                        city = row[5].strip() if row[5].strip() else row[4].strip()
                        upload_lines.append(f"{ip}:{port}#{city}")

            with open(output_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(upload_lines))

            self.log(f"✓ 转换完成，生成 {len(upload_lines)} 条记录")
            return True

        except Exception as e:
            self.log(f"转换失败: {e}", "ERROR")
            return False

    def upload_to_gist(self, file_path):
        """上传文件到GitHub Gist"""
        if not GITHUB_TOKEN:
            self.log("未配置GITHUB_TOKEN，跳过Gist上传")
            return False

        self.log(f"开始上传 {file_path} 到GitHub Gist...")

        try:
            if not os.path.exists(file_path):
                self.log(f"文件 {file_path} 不存在")
                return False

            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            data = {
                "files": {
                    os.path.basename(file_path): {
                        "content": content
                    }
                }
            }

            json_data = json.dumps(data).encode('utf-8')

            req = Request(
                f"https://api.github.com/gists/{GIST_ID}",
                data=json_data,
                headers={
                    "Authorization": f"token {GITHUB_TOKEN}",
                    "Accept": "application/vnd.github+json",
                    "Content-Type": "application/json"
                },
                method='PATCH'
            )

            self.log(f"发送PATCH请求到GitHub API...")

            with urlopen(req, timeout=30) as response:
                if response.status in (200, 201):
                    self.log(f"✓ {file_path} 上传到GitHub Gist成功")
                    self.log(f"Gist URL: https://gist.github.com/{GITHUB_USER}/{GIST_ID}")
                    self.log(f"Raw URL: https://gist.githubusercontent.com/{GITHUB_USER}/{GIST_ID}/raw/{os.path.basename(file_path)}")
                    return True
                else:
                    self.log(f"上传失败，状态码: {response.status}")
                    return False

        except HTTPError as e:
            self.log(f"HTTP错误: {e.code} - {e.reason}")
            try:
                error_body = e.read().decode('utf-8')
                self.log(f"错误详情: {error_body}")
            except:
                pass
            return False
        except Exception as e:
            self.log(f"上传出错: {str(e)}", "ERROR")
            return False

    def test_all_regions(self, test_count=3, max_delay=200, min_speed=10):
        """测试所有地区"""
        self.log("开始测试所有地区...")

        result_files = []

        # 测试IPv4地区
        for region in REGIONS:
            result_file = self.test_region(region, test_count, max_delay, min_speed)
            if result_file:
                result_files.append(result_file)

        # 测试IPv6
        ipv6_result = self.test_ipv6(test_count, max_delay, min_speed)
        if ipv6_result:
            result_files.append(ipv6_result)

        # 合并结果
        final_result = self.base_dir / 'final_result.csv'
        if self.merge_results(result_files, final_result):
            self.log(f"✓ 所有地区测试完成，结果保存在 {final_result}")
            return final_result
        else:
            return None

    def test_common_regions(self, test_count=3, max_delay=200, min_speed=10):
        """测试常用地区 (香港、日本、新加坡、IPv6)"""
        self.log("开始测试常用地区...")

        result_files = []

        # 测试常用IPv4地区
        for region in COMMON_REGIONS:
            result_file = self.test_region(region, test_count, max_delay, min_speed)
            if result_file:
                result_files.append(result_file)

        # 测试IPv6
        ipv6_result = self.test_ipv6(test_count, max_delay, min_speed)
        if ipv6_result:
            result_files.append(ipv6_result)

        # 合并结果
        common_result = self.base_dir / 'cfips_common.csv'
        if self.merge_results(result_files, common_result):
            self.log(f"✓ 常用地区测试完成，结果保存在 {common_result}")

            # 转换为上传格式
            upload_file = self.base_dir / 'cfips_common.txt'
            if self.convert_to_upload_format(common_result, upload_file):
                return common_result, upload_file
            else:
                return common_result, None
        else:
            return None, None

    def interactive_menu(self):
        """交互式菜单"""
        while True:
            print("\n" + "="*60)
            print("           Cloudflare IP 测试工具 - Python版")
            print("="*60)
            print("1. 更新IP列表")
            print("2. 测试常用地区 (香港/日本/新加坡/IPv6)")
            print("3. 测试所有地区")
            print("4. 测试指定地区")
            print("5. 上传结果到GitHub Gist")
            print("6. 退出")
            print("="*60)

            try:
                choice = input("请选择功能 (1-6): ").strip()

                if choice == '1':
                    self.download_ip_lists()

                elif choice == '2':
                    print("\n配置测试参数:")
                    test_count = int(input("测试次数 (默认3): ") or "3")
                    max_delay = int(input("最大延迟ms (默认200): ") or "200")
                    min_speed = int(input("最低速度MB/s (默认10): ") or "10")

                    csv_file, txt_file = self.test_common_regions(test_count, max_delay, min_speed)
                    if csv_file:
                        print(f"\n测试完成！结果文件:")
                        print(f"- CSV格式: {csv_file}")
                        if txt_file:
                            print(f"- 上传格式: {txt_file}")

                        upload = input("\n是否上传到GitHub Gist? (y/N): ").strip().lower()
                        if upload == 'y' and txt_file:
                            self.upload_to_gist(txt_file)

                elif choice == '3':
                    print("\n配置测试参数:")
                    test_count = int(input("测试次数 (默认3): ") or "3")
                    max_delay = int(input("最大延迟ms (默认200): ") or "200")
                    min_speed = int(input("最低速度MB/s (默认10): ") or "10")

                    result_file = self.test_all_regions(test_count, max_delay, min_speed)
                    if result_file:
                        print(f"\n测试完成！结果文件: {result_file}")

                elif choice == '4':
                    print("\n可用地区:")
                    for i, (code, name) in enumerate(REGIONS.items(), 1):
                        print(f"{i}. {name} ({code})")

                    try:
                        region_choice = int(input("选择地区 (1-9): ")) - 1
                        region_codes = list(REGIONS.keys())
                        if 0 <= region_choice < len(region_codes):
                            region = region_codes[region_choice]

                            print("\n配置测试参数:")
                            test_count = int(input("测试次数 (默认3): ") or "3")
                            max_delay = int(input("最大延迟ms (默认200): ") or "200")
                            min_speed = int(input("最低速度MB/s (默认10): ") or "10")

                            result_file = self.test_region(region, test_count, max_delay, min_speed)
                            if result_file:
                                print(f"\n测试完成！结果文件: {result_file}")
                        else:
                            print("无效的选择")
                    except ValueError:
                        print("请输入有效的数字")

                elif choice == '5':
                    # 查找可上传的文件
                    upload_files = []
                    if (self.base_dir / 'cfips_common.txt').exists():
                        upload_files.append(('cfips_common.txt', '常用地区结果'))
                    if (self.base_dir / 'final_result.csv').exists():
                        upload_files.append(('final_result.csv', '所有地区结果'))

                    if upload_files:
                        print("\n可上传的文件:")
                        for i, (filename, desc) in enumerate(upload_files, 1):
                            print(f"{i}. {desc} ({filename})")

                        try:
                            file_choice = int(input("选择文件 (1-{}): ".format(len(upload_files)))) - 1
                            if 0 <= file_choice < len(upload_files):
                                filename = upload_files[file_choice][0]
                                self.upload_to_gist(self.base_dir / filename)
                            else:
                                print("无效的选择")
                        except ValueError:
                            print("请输入有效的数字")
                    else:
                        print("没有找到可上传的文件，请先运行测试")

                elif choice == '6':
                    print("退出程序")
                    break

                else:
                    print("无效的选择，请重新输入")

            except KeyboardInterrupt:
                print("\n\n程序被用户中断")
                break
            except Exception as e:
                print(f"发生错误: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Cloudflare IP测试工具')
    parser.add_argument('--update', action='store_true', help='更新IP列表')
    parser.add_argument('--test-common', action='store_true', help='测试常用地区')
    parser.add_argument('--test-all', action='store_true', help='测试所有地区')
    parser.add_argument('--test-region', help='测试指定地区')
    parser.add_argument('--upload', help='上传文件到Gist')
    parser.add_argument('--count', type=int, default=3, help='测试次数')
    parser.add_argument('--delay', type=int, default=200, help='最大延迟(ms)')
    parser.add_argument('--speed', type=int, default=10, help='最低速度(MB/s)')

    args = parser.parse_args()

    cfip = CFIPTest()

    # 如果没有参数，启动交互模式
    if len(sys.argv) == 1:
        cfip.interactive_menu()
        return

    # 命令行模式
    if args.update:
        cfip.download_ip_lists()

    if args.test_common:
        csv_file, txt_file = cfip.test_common_regions(args.count, args.delay, args.speed)
        if txt_file:
            print(f"常用地区测试完成: {txt_file}")

    if args.test_all:
        result_file = cfip.test_all_regions(args.count, args.delay, args.speed)
        if result_file:
            print(f"所有地区测试完成: {result_file}")

    if args.test_region:
        if args.test_region in REGIONS:
            result_file = cfip.test_region(args.test_region, args.count, args.delay, args.speed)
            if result_file:
                print(f"地区测试完成: {result_file}")
        else:
            print(f"无效的地区代码: {args.test_region}")
            print(f"可用地区: {', '.join(REGIONS.keys())}")

    if args.upload:
        cfip.upload_to_gist(args.upload)

if __name__ == "__main__":
    main()
