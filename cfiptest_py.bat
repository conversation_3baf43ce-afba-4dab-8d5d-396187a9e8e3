@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

title Cloudflare IP Test Tool - Python版

echo ======================================================
echo           Cloudflare IP Test Tool - Python版
echo ======================================================
echo.

rem 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.6+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

rem 检查脚本文件是否存在
if not exist "py\cfiptest.py" (
    echo 错误: 未找到 py\cfiptest.py 脚本文件
    pause
    exit /b 1
)

rem 启动Python脚本
echo 启动Python脚本...
echo.
python py\cfiptest.py %*

rem 如果没有参数，暂停等待用户按键
if "%~1"=="" (
    echo.
    pause
)
